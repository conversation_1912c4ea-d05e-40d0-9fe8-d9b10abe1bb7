# PlaceActivityTopic 和 PlaceActivityTopicSchedule 确认状态重置功能

## 功能概述

当主题或日程的关键字段发生修改时，系统会自动重置相关表的确认状态，要求用户重新确认。

## 主题级别 (PlaceActivityTopic)

### 触发条件

当 `place_activity_topic` 表的以下任一字段发生变化时，会触发确认状态重置：

1. **name** - 主题名称
2. **start_time** - 开始时间
3. **end_time** - 结束时间
4. **place_id** - 场地ID

### 影响的表和字段

1. **place_activity_topic_guest**
   - `confirm_status` 重置为 0（未确认）

2. **place_activity_topic_speaker**
   - `confirm_status` 重置为 0（未确认）

3. **place_activity_topic_discuss**
   - `confirm_status` 重置为 0（未确认）

4. **activity_guest**
   - `is_service` 重置为 0（未服务）

## 日程级别 (PlaceActivityTopicSchedule)

### 触发条件

当 `place_activity_topic_schedule` 表的以下任一字段发生变化时，会触发确认状态重置：

1. **name** - 日程名称
2. **start_time** - 开始时间
3. **end_time** - 结束时间

### 影响的表和字段

1. **place_activity_topic_schedule_guest**
   - `confirm_status` 重置为 0（未确认）

2. **place_activity_topic_schedule_speaker**
   - `confirm_status` 重置为 0（未确认）

3. **place_activity_topic_schedule_discuss**
   - `confirm_status` 重置为 0（未确认）

4. **activity_guest**
   - `is_service` 重置为 0（未服务）

## 实现逻辑

### 主题级别实现 (PlaceActivityTopicController)

#### 1. 变更检测

在 `PlaceActivityTopicController.update()` 方法中：

```java
// 获取更新前的数据，检查关键字段是否发生变化
PlaceActivityTopicEntity oldTopic = placeActivityTopicService.getById(placeActivityTopic.getId());
boolean needResetConfirmStatus = false;

if (oldTopic != null) {
    // 检查名称、时间、场地是否发生变化
    if (!Objects.equals(oldTopic.getName(), placeActivityTopic.getName()) ||
        !Objects.equals(oldTopic.getStartTime(), placeActivityTopic.getStartTime()) ||
        !Objects.equals(oldTopic.getEndTime(), placeActivityTopic.getEndTime()) ||
        !Objects.equals(oldTopic.getPlaceId(), placeActivityTopic.getPlaceId())) {
        needResetConfirmStatus = true;
    }
}
```

#### 2. 确认状态重置

如果检测到关键字段变化，调用重置方法：

```java
// 如果关键字段发生变化，重置相关确认状态
if (needResetConfirmStatus) {
    resetConfirmStatusForTopic(placeActivityTopic.getId(), activityGuestIds);
}
```

#### 3. 重置实现

`resetConfirmStatusForTopic()` 方法执行以下操作：

1. **重置主题级别确认状态**：
   - 查询该主题下的所有 guest、speaker、discuss 记录
   - 将 `confirm_status` 设置为 0
   - 批量更新数据库

2. **重置活动嘉宾服务状态**：
   - 将相关嘉宾的 `is_service` 设置为 0
   - 批量更新数据库

### 日程级别实现 (PlaceActivityTopicScheduleController)

#### 1. 变更检测

在 `PlaceActivityTopicScheduleController.update()` 方法中：

```java
// 获取更新前的数据，检查关键字段是否发生变化
PlaceActivityTopicScheduleEntity oldSchedule = placeActivityTopicScheduleService.getById(placeActivityTopicSchedule.getId());
boolean needResetConfirmStatus = false;

if (oldSchedule != null) {
    // 检查名称、时间是否发生变化
    if (!Objects.equals(oldSchedule.getName(), placeActivityTopicSchedule.getName()) ||
        !Objects.equals(oldSchedule.getStartTime(), placeActivityTopicSchedule.getStartTime()) ||
        !Objects.equals(oldSchedule.getEndTime(), placeActivityTopicSchedule.getEndTime())) {
        needResetConfirmStatus = true;
    }
}
```

#### 2. 确认状态重置

如果检测到关键字段变化，调用重置方法：

```java
// 如果关键字段发生变化，重置相关确认状态
if (needResetConfirmStatus) {
    resetConfirmStatusForSchedule(placeActivityTopicSchedule.getId(), activityGuestIds);
}
```

#### 3. 重置实现

`resetConfirmStatusForSchedule()` 方法执行以下操作：

1. **重置日程级别确认状态**：
   - 查询该日程下的所有 guest、speaker、discuss 记录
   - 将 `confirm_status` 设置为 0
   - 批量更新数据库

2. **重置活动嘉宾服务状态**：
   - 将相关嘉宾的 `is_service` 设置为 0
   - 批量更新数据库

## 代码位置

### 主题级别
- **主要实现文件**: `mp-joy-service/src/main/java/com/cjy/mp/modules/place/controller/PlaceActivityTopicController.java`
- **测试文件**: `mp-joy-service/src/test/java/com/cjy/mp/modules/place/controller/PlaceActivityTopicControllerTest.java`

### 日程级别
- **主要实现文件**: `mp-joy-service/src/main/java/com/cjy/mp/modules/place/controller/PlaceActivityTopicScheduleController.java`
- **测试文件**: `mp-joy-service/src/test/java/com/cjy/mp/modules/place/controller/PlaceActivityTopicScheduleControllerTest.java`

## 使用场景

### 主题级别
1. **会议主题名称修改**：当主题名称发生变化时，需要重新确认所有相关人员
2. **会议时间调整**：当主题开始或结束时间变化时，需要重新确认时间安排
3. **会议场地变更**：当场地发生变化时，需要重新确认场地安排

### 日程级别
1. **日程名称修改**：当日程名称发生变化时，需要重新确认相关人员
2. **日程时间调整**：当日程开始或结束时间变化时，需要重新确认时间安排

## 注意事项

1. **性能考虑**：使用批量更新操作，避免逐条更新造成的性能问题
2. **事务安全**：确保在事务中执行，保证数据一致性
3. **分层处理**：主题级别和日程级别分别处理，避免重复操作
4. **用户体验**：用户需要重新确认相关信息，确保信息准确性

## 测试验证

运行测试用例验证功能：

```bash
# 测试主题级别功能
mvn test -Dtest=PlaceActivityTopicControllerTest

# 测试日程级别功能
mvn test -Dtest=PlaceActivityTopicScheduleControllerTest
```

测试覆盖场景：
- 关键字段变化时确认状态被正确重置
- 非关键字段变化时确认状态不受影响
- 批量更新操作正确执行
- 主题和日程分别独立处理
