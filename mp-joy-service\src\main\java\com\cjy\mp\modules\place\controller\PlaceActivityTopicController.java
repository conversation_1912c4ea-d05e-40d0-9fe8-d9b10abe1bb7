package com.cjy.mp.modules.place.controller;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.cjy.mp.common.utils.DateUtils;
import com.cjy.mp.modules.activity.entity.ActivityEntity;
import com.cjy.mp.modules.activity.entity.ActivityGuestEntity;
import com.cjy.mp.modules.activity.service.ActivityGuestService;
import com.cjy.mp.modules.activity.service.ActivityService;
import com.cjy.mp.modules.place.entity.PlaceActivityTopicDiscussEntity;
import com.cjy.mp.modules.place.entity.PlaceActivityTopicGuestEntity;
import com.cjy.mp.modules.place.entity.PlaceActivityTopicSpeakerEntity;
import com.cjy.mp.modules.place.service.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.cjy.mp.modules.place.entity.PlaceActivityTopicEntity;
import com.cjy.mp.common.utils.PageUtils;
import com.cjy.mp.common.utils.R;



/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-09 10:49:40
 */
@RestController
@RequestMapping("place/placeactivitytopic")
public class PlaceActivityTopicController {
    @Autowired
    private PlaceActivityTopicService placeActivityTopicService;
    @Autowired
    private PlaceActivityTopicGuestService placeActivityTopicGuestService;
    @Autowired
    private PlaceActivityTopicSpeakerService placeActivityTopicSpeakerService;
    @Autowired
    private ActivityGuestService activityGuestService;
    @Autowired
    private PlaceActivityTopicScheduleService placeActivityTopicScheduleService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private PlaceActivityTopicDiscussService placeActivityTopicDiscussService;

    @RequestMapping("/findByActivityId/{activityId}")
    public R findByActivityId(@PathVariable("activityId") Long activityId){
        List<PlaceActivityTopicEntity> placeActivityTopicEntities = placeActivityTopicService.findByActivityId(activityId);
        placeActivityTopicEntities.forEach(e-> {
            e.setWeek(e.getStartTime() == null ? "-" : DateUtils.getWeek(e.getStartTime()));
        });
        return R.ok().put("result", placeActivityTopicEntities);
    }

    @RequestMapping("/findByPlaceId/{placeActivityId}")
    public R findByPlaceActivityId(@PathVariable("placeActivityId") Long placeActivityId){
        return R.ok().put("result",placeActivityTopicService.findByPlaceId(placeActivityId));
    }

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("place:placeactivitytopic:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = placeActivityTopicService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("place:placeactivitytopic:info")
    public R info(@PathVariable("id") Long id){
		PlaceActivityTopicEntity placeActivityTopic = placeActivityTopicService.getById(id);
        List<PlaceActivityTopicGuestEntity> placeActivityTopicGuestEntities = placeActivityTopicGuestService.selectByTopicId(id);
        List<Long> topicGusestIds = placeActivityTopicGuestEntities.stream().map(PlaceActivityTopicGuestEntity::getActivityGuestId).collect(Collectors.toList());
        placeActivityTopic.setTopicGuestIds(topicGusestIds);
        List<PlaceActivityTopicSpeakerEntity> placeActivityTopicSpeakerEntities = placeActivityTopicSpeakerService.selectByTopicId(id);
        List<Long> topicSpeakerIds = placeActivityTopicSpeakerEntities.stream().map(PlaceActivityTopicSpeakerEntity::getActivityGuestId).collect(Collectors.toList());
        placeActivityTopic.setTopicSpeakerIds(topicSpeakerIds);
        List<PlaceActivityTopicDiscussEntity> placeActivityTopicDiscussEntities = placeActivityTopicDiscussService.selectByTopicId(id);
        List<Long> topicDiscussIds = placeActivityTopicDiscussEntities.stream().map(PlaceActivityTopicDiscussEntity::getActivityGuestId).collect(Collectors.toList());
        placeActivityTopic.setTopicDiscussIds(topicDiscussIds);
        return R.ok().put("placeActivityTopic", placeActivityTopic);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("place:placeactivitytopic:save")
    public R save(@RequestBody PlaceActivityTopicEntity placeActivityTopic){
        // 校验日程
//        R result = placeActivityTopicService.checkTopicTime(placeActivityTopic);
//        if ((int) result.get("code") == 500) {
//            return result;
//        }
        ActivityEntity activityEntity = activityService.getById(placeActivityTopic.getActivityId());
        placeActivityTopicService.save(placeActivityTopic);
        // 保存主持
        if (placeActivityTopic.getTopicGuestIds().size() > 0) {
            Map<Long, ActivityGuestEntity> activityGuestEntities = activityGuestService.findByIds(placeActivityTopic.getTopicGuestIds()).stream().collect(Collectors.toMap(ActivityGuestEntity::getId, Function.identity(),(e, v) -> e));
            List<PlaceActivityTopicGuestEntity> placeActivityTopicGuestEntities = placeActivityTopic.getTopicGuestIds().stream().map(e -> {
                PlaceActivityTopicGuestEntity placeActivityTopicGuest = new PlaceActivityTopicGuestEntity();
                placeActivityTopicGuest.setActivityId(placeActivityTopic.getActivityId());
                placeActivityTopicGuest.setActivityGuestId(e);
                placeActivityTopicGuest.setServiceFee(activityEntity.getTopicGuest());
                placeActivityTopicGuest.setPlaceActivityTopicId(placeActivityTopic.getId());
                ActivityGuestEntity activityGuestEntity = activityGuestEntities.get(e);
                placeActivityTopicGuest.setName(activityGuestEntity.getName());
                placeActivityTopicGuest.setMobile(activityGuestEntity.getMobile());
                placeActivityTopicGuest.setDuties(activityGuestEntity.getDuties());
                placeActivityTopicGuest.setUnit(activityGuestEntity.getUnit());
                return placeActivityTopicGuest;
            }).collect(Collectors.toList());
            placeActivityTopicGuestService.saveBatch(placeActivityTopicGuestEntities);
        }
        // 保存主席
        if (placeActivityTopic.getTopicSpeakerIds().size() > 0) {
            Map<Long, ActivityGuestEntity> activityGuestEntities = activityGuestService.findByIds(placeActivityTopic.getTopicSpeakerIds()).stream().collect(Collectors.toMap(ActivityGuestEntity::getId, Function.identity(),(e, v) -> e));
            List<PlaceActivityTopicSpeakerEntity> placeActivityTopicSpeakerEntities = placeActivityTopic.getTopicSpeakerIds().stream().map(e -> {
                PlaceActivityTopicSpeakerEntity placeActivityTopicSpeaker = new PlaceActivityTopicSpeakerEntity();
                placeActivityTopicSpeaker.setActivityId(placeActivityTopic.getActivityId());
                placeActivityTopicSpeaker.setActivityGuestId(e);
                placeActivityTopicSpeaker.setServiceFee(activityEntity.getTopicSpeaker());
                placeActivityTopicSpeaker.setPlaceActivityTopicId(placeActivityTopic.getId());
                ActivityGuestEntity activityGuestEntity = activityGuestEntities.get(e);
                placeActivityTopicSpeaker.setName(activityGuestEntity.getName());
                placeActivityTopicSpeaker.setMobile(activityGuestEntity.getMobile());
                placeActivityTopicSpeaker.setDuties(activityGuestEntity.getDuties());
                placeActivityTopicSpeaker.setUnit(activityGuestEntity.getUnit());
                return placeActivityTopicSpeaker;
            }).collect(Collectors.toList());
            placeActivityTopicSpeakerService.saveBatch(placeActivityTopicSpeakerEntities);
        }
        // 保存讨论
        if (placeActivityTopic.getTopicDiscussIds().size() > 0) {
            Map<Long, ActivityGuestEntity> activityGuestEntities = activityGuestService.findByIds(placeActivityTopic.getTopicDiscussIds()).stream().collect(Collectors.toMap(ActivityGuestEntity::getId, Function.identity(),(e, v) -> e));
            List<PlaceActivityTopicDiscussEntity> placeActivityTopicDiscussEntities = placeActivityTopic.getTopicDiscussIds().stream().map(e -> {
                PlaceActivityTopicDiscussEntity placeActivityTopicDiscuss = new PlaceActivityTopicDiscussEntity();
                placeActivityTopicDiscuss.setActivityId(placeActivityTopic.getActivityId());
                placeActivityTopicDiscuss.setActivityGuestId(e);
                placeActivityTopicDiscuss.setServiceFee(activityEntity.getTopicDiscuss());
                placeActivityTopicDiscuss.setPlaceActivityTopicId(placeActivityTopic.getId());
                ActivityGuestEntity activityGuestEntity = activityGuestEntities.get(e);
                placeActivityTopicDiscuss.setName(activityGuestEntity.getName());
                placeActivityTopicDiscuss.setMobile(activityGuestEntity.getMobile());
                placeActivityTopicDiscuss.setDuties(activityGuestEntity.getDuties());
                placeActivityTopicDiscuss.setUnit(activityGuestEntity.getUnit());
                return placeActivityTopicDiscuss;
            }).collect(Collectors.toList());
            placeActivityTopicDiscussService.saveBatch(placeActivityTopicDiscussEntities);
        }
        // 合并用户ID
        List<Long> activityGuestIds = Stream.of(placeActivityTopic.getTopicGuestIds(), placeActivityTopic.getTopicSpeakerIds(), placeActivityTopic.getTopicDiscussIds()).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        activityGuestService.updateServiceFeeIds(activityGuestIds);
        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("place:placeactivitytopic:update")
    public R update(@RequestBody PlaceActivityTopicEntity placeActivityTopic) {
        // 校验日程
//        R result = placeActivityTopicService.checkTopicTime(placeActivityTopic);
//        if ((int) result.get("code") == 500) {
//            return result;
//        }
        ActivityEntity activityEntity = activityService.getById(placeActivityTopic.getActivityId());

        // 获取更新前的数据，检查关键字段是否发生变化
        PlaceActivityTopicEntity oldTopic = placeActivityTopicService.getById(placeActivityTopic.getId());
        boolean needResetConfirmStatus = false;

        if (oldTopic != null) {
            // 检查名称、时间、场地是否发生变化
            if (!Objects.equals(oldTopic.getName(), placeActivityTopic.getName()) ||
                !Objects.equals(oldTopic.getStartTime(), placeActivityTopic.getStartTime()) ||
                !Objects.equals(oldTopic.getEndTime(), placeActivityTopic.getEndTime()) ||
                !Objects.equals(oldTopic.getPlaceId(), placeActivityTopic.getPlaceId())) {
                needResetConfirmStatus = true;
            }
        }

		placeActivityTopicService.updateById(placeActivityTopic);
        // 主持
        List<PlaceActivityTopicGuestEntity> placeActivityTopicGuestEntities = placeActivityTopicGuestService.selectByTopicId(placeActivityTopic.getId());
        List<Long> exitTopicGuestIds = placeActivityTopicGuestEntities.stream().map(PlaceActivityTopicGuestEntity::getActivityGuestId).collect(Collectors.toList());
        List<Long> exitTopicScheduleGuestIds = placeActivityTopicGuestEntities.stream().map(PlaceActivityTopicGuestEntity::getId).collect(Collectors.toList());
        List<Long> schedulesGuestIds = placeActivityTopic.getTopicGuestIds();
        if (schedulesGuestIds.size() > 0) {
            Map<Long, ActivityGuestEntity> activityGuestEntities = activityGuestService.findByIds(schedulesGuestIds).stream().collect(Collectors.toMap(ActivityGuestEntity::getId, Function.identity(),(e, v) -> e));
            // 找到删除
            List<Long> deleteGuest = placeActivityTopicGuestEntities.stream().filter(e -> !schedulesGuestIds.contains(e.getActivityGuestId())).map(PlaceActivityTopicGuestEntity::getId).collect(Collectors.toList());
            List<PlaceActivityTopicGuestEntity> saveGuest = schedulesGuestIds.stream().filter(f-> !exitTopicGuestIds.contains(f)).map(e -> {
                PlaceActivityTopicGuestEntity placeActivityTopicGuest = new PlaceActivityTopicGuestEntity();
                placeActivityTopicGuest.setActivityId(placeActivityTopic.getActivityId());
                placeActivityTopicGuest.setActivityGuestId(e);
                placeActivityTopicGuest.setServiceFee(activityEntity.getTopicGuest());
                placeActivityTopicGuest.setPlaceActivityTopicId(placeActivityTopic.getId());
                ActivityGuestEntity activityGuestEntity = activityGuestEntities.get(e);
                placeActivityTopicGuest.setName(activityGuestEntity.getName());
                placeActivityTopicGuest.setMobile(activityGuestEntity.getMobile());
                placeActivityTopicGuest.setDuties(activityGuestEntity.getDuties());
                placeActivityTopicGuest.setUnit(activityGuestEntity.getUnit());
                return placeActivityTopicGuest;
            }).collect(Collectors.toList());
            if (deleteGuest.size() > 0) {
                placeActivityTopicGuestService.removeByIds(deleteGuest);
            }
            if (saveGuest.size() > 0) {
                placeActivityTopicGuestService.saveBatch(saveGuest);
            }
        } else {
            if (placeActivityTopicGuestEntities.size() > 0) {
                placeActivityTopicGuestService.removeByIds(exitTopicScheduleGuestIds);
            }
        }
        // 主席
        List<PlaceActivityTopicSpeakerEntity> placeActivityTopicSpeakerEntities = placeActivityTopicSpeakerService.selectByTopicId(placeActivityTopic.getId());
        List<Long> exitTopicSpeakerIds = placeActivityTopicSpeakerEntities.stream().map(PlaceActivityTopicSpeakerEntity::getActivityGuestId).collect(Collectors.toList());
        List<Long> exitTopicScheduleSpeakerIds = placeActivityTopicSpeakerEntities.stream().map(PlaceActivityTopicSpeakerEntity::getId).collect(Collectors.toList());
        List<Long> schedulesSpeakerIds = placeActivityTopic.getTopicSpeakerIds();
        if (schedulesSpeakerIds.size() > 0) {
            Map<Long, ActivityGuestEntity> activityGuestEntities = activityGuestService.findByIds(schedulesSpeakerIds).stream().collect(Collectors.toMap(ActivityGuestEntity::getId, Function.identity(),(e, v) -> e));
            // 找到删除
            List<Long> deleteSpeaker = placeActivityTopicSpeakerEntities.stream().filter(e -> !schedulesSpeakerIds.contains(e.getActivityGuestId())).map(PlaceActivityTopicSpeakerEntity::getId).collect(Collectors.toList());
            List<PlaceActivityTopicSpeakerEntity> saveSpeaker = schedulesSpeakerIds.stream().filter(f-> !exitTopicSpeakerIds.contains(f)).map(e -> {
                PlaceActivityTopicSpeakerEntity placeActivityTopicSpeaker = new PlaceActivityTopicSpeakerEntity();
                placeActivityTopicSpeaker.setActivityId(placeActivityTopic.getActivityId());
                placeActivityTopicSpeaker.setActivityGuestId(e);
                placeActivityTopicSpeaker.setServiceFee(activityEntity.getTopicSpeaker());
                placeActivityTopicSpeaker.setPlaceActivityTopicId(placeActivityTopic.getId());
                ActivityGuestEntity activityGuestEntity = activityGuestEntities.get(e);
                placeActivityTopicSpeaker.setName(activityGuestEntity.getName());
                placeActivityTopicSpeaker.setMobile(activityGuestEntity.getMobile());
                placeActivityTopicSpeaker.setDuties(activityGuestEntity.getDuties());
                placeActivityTopicSpeaker.setUnit(activityGuestEntity.getUnit());
                return placeActivityTopicSpeaker;
            }).collect(Collectors.toList());
            if (deleteSpeaker.size() > 0) {
                placeActivityTopicSpeakerService.removeByIds(deleteSpeaker);
            }
            if (saveSpeaker.size() > 0) {
                placeActivityTopicSpeakerService.saveBatch(saveSpeaker);
            }
        } else {
            if (placeActivityTopicSpeakerEntities.size() > 0) {
                placeActivityTopicSpeakerService.removeByIds(exitTopicScheduleSpeakerIds);
            }
        }
        // 主席
        List<PlaceActivityTopicDiscussEntity> placeActivityTopicDiscussEntities = placeActivityTopicDiscussService.selectByTopicId(placeActivityTopic.getId());
        List<Long> exitTopicDiscussIds = placeActivityTopicDiscussEntities.stream().map(PlaceActivityTopicDiscussEntity::getActivityGuestId).collect(Collectors.toList());
        List<Long> exitTopicScheduleDiscussIds = placeActivityTopicDiscussEntities.stream().map(PlaceActivityTopicDiscussEntity::getId).collect(Collectors.toList());
        List<Long> schedulesDiscussIds = placeActivityTopic.getTopicDiscussIds();
        if (schedulesDiscussIds.size() > 0) {
            Map<Long, ActivityGuestEntity> activityGuestEntities = activityGuestService.findByIds(schedulesDiscussIds).stream().collect(Collectors.toMap(ActivityGuestEntity::getId, Function.identity(),(e, v) -> e));
            // 找到删除
            List<Long> deleteDiscuss = placeActivityTopicDiscussEntities.stream().filter(e -> !schedulesDiscussIds.contains(e.getActivityGuestId())).map(PlaceActivityTopicDiscussEntity::getId).collect(Collectors.toList());
            List<PlaceActivityTopicDiscussEntity> saveDiscuss = schedulesDiscussIds.stream().filter(f-> !exitTopicDiscussIds.contains(f)).map(e -> {
                PlaceActivityTopicDiscussEntity placeActivityTopicDiscuss = new PlaceActivityTopicDiscussEntity();
                placeActivityTopicDiscuss.setActivityId(placeActivityTopic.getActivityId());
                placeActivityTopicDiscuss.setActivityGuestId(e);
                placeActivityTopicDiscuss.setServiceFee(activityEntity.getTopicDiscuss());
                placeActivityTopicDiscuss.setPlaceActivityTopicId(placeActivityTopic.getId());
                ActivityGuestEntity activityGuestEntity = activityGuestEntities.get(e);
                placeActivityTopicDiscuss.setName(activityGuestEntity.getName());
                placeActivityTopicDiscuss.setMobile(activityGuestEntity.getMobile());
                placeActivityTopicDiscuss.setDuties(activityGuestEntity.getDuties());
                placeActivityTopicDiscuss.setUnit(activityGuestEntity.getUnit());
                return placeActivityTopicDiscuss;
            }).collect(Collectors.toList());
            if (deleteDiscuss.size() > 0) {
                placeActivityTopicDiscussService.removeByIds(deleteDiscuss);
            }
            if (saveDiscuss.size() > 0) {
                placeActivityTopicDiscussService.saveBatch(saveDiscuss);
            }
        } else {
            if (placeActivityTopicDiscussEntities.size() > 0) {
                placeActivityTopicDiscussService.removeByIds(exitTopicScheduleDiscussIds);
            }
        }
        // 合并用户ID
        List<Long> activityGuestIds = Stream.of(schedulesGuestIds, schedulesSpeakerIds, schedulesDiscussIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        activityGuestService.updateServiceFeeIds(activityGuestIds);

        // 如果关键字段发生变化，重置相关确认状态
        if (needResetConfirmStatus) {
            resetConfirmStatusForTopic(placeActivityTopic.getId(), activityGuestIds);
        }

        return R.ok();
    }

    /**
     * 重置主题相关的确认状态
     * @param topicId 主题ID
     * @param activityGuestIds 相关的嘉宾ID列表
     */
    private void resetConfirmStatusForTopic(Long topicId, List<Long> activityGuestIds) {
        // 重置place_activity_topic_guest的confirm_status为0（未确认）
        List<PlaceActivityTopicGuestEntity> topicGuests = placeActivityTopicGuestService.selectByTopicId(topicId);
        if (!topicGuests.isEmpty()) {
            topicGuests.forEach(guest -> guest.setConfirmStatus(0));
            placeActivityTopicGuestService.updateBatchById(topicGuests);
        }

        // 重置place_activity_topic_speaker的confirm_status为0（未确认）
        List<PlaceActivityTopicSpeakerEntity> topicSpeakers = placeActivityTopicSpeakerService.selectByTopicId(topicId);
        if (!topicSpeakers.isEmpty()) {
            topicSpeakers.forEach(speaker -> speaker.setConfirmStatus(0));
            placeActivityTopicSpeakerService.updateBatchById(topicSpeakers);
        }

        // 重置place_activity_topic_discuss的confirm_status为0（未确认）
        List<PlaceActivityTopicDiscussEntity> topicDiscusses = placeActivityTopicDiscussService.selectByTopicId(topicId);
        if (!topicDiscusses.isEmpty()) {
            topicDiscusses.forEach(discuss -> discuss.setConfirmStatus(0));
            placeActivityTopicDiscussService.updateBatchById(topicDiscusses);
        }

        // 重置相关activity_guest的is_service为0
        if (activityGuestIds != null && !activityGuestIds.isEmpty()) {
            List<ActivityGuestEntity> activityGuests = activityGuestService.findByIds(activityGuestIds);
            if (!activityGuests.isEmpty()) {
                activityGuests.forEach(guest -> guest.setIsSchedule(0));
                activityGuestService.updateBatchById(activityGuests);
            }
        }
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("place:placeactivitytopic:delete")
    public R delete(@RequestBody Long[] ids){
		placeActivityTopicService.removeByIds(Arrays.asList(ids));

        // 删除主持人
        List<PlaceActivityTopicGuestEntity> placeActivityTopicScheduleGuestEntities = placeActivityTopicGuestService.selectByTopicIds(Arrays.asList(ids));
        placeActivityTopicGuestService.removeByIds(placeActivityTopicScheduleGuestEntities.stream().map(PlaceActivityTopicGuestEntity::getId).collect(Collectors.toList()));
        List<PlaceActivityTopicDiscussEntity> placeActivityTopicScheduleDiscussEntities = placeActivityTopicDiscussService.selectByTopicIds(Arrays.asList(ids));
        placeActivityTopicDiscussService.removeByIds(placeActivityTopicScheduleDiscussEntities.stream().map(PlaceActivityTopicDiscussEntity::getId).collect(Collectors.toList()));
        List<PlaceActivityTopicSpeakerEntity> placeActivityTopicScheduleSpeakerEntities = placeActivityTopicSpeakerService.selectByTopicIds(Arrays.asList(ids));
        placeActivityTopicSpeakerService.removeByIds(placeActivityTopicScheduleSpeakerEntities.stream().map(PlaceActivityTopicSpeakerEntity::getId).collect(Collectors.toList()));
        return R.ok();
    }

    /**
     * 统计将要清理的孤立关联数据数量
     */
    @RequestMapping("/countOrphanedRelations")
    @RequiresPermissions("place:placeactivitytopic:list")
    public R countOrphanedRelations(@RequestParam Map<String, Object> params){
        List<Map<String, Object>> result = placeActivityTopicService.countOrphanedRelations(params);
        return R.ok().put("result", result);
    }

    /**
     * 清理孤立的关联数据
     */
    @RequestMapping("/cleanOrphanedRelations")
    @RequiresPermissions("place:placeactivitytopic:delete")
    public R cleanOrphanedRelations(@RequestParam Map<String, Object> params){
        // 先统计要清理的数据
        List<Map<String, Object>> countResult = placeActivityTopicService.countOrphanedRelations(params);

        // 执行清理
        Map<String, Object> cleanResult = placeActivityTopicService.cleanOrphanedRelations(params);

        // 返回清理前统计和清理结果
        Map<String, Object> result = new HashMap<>();
        result.put("beforeClean", countResult);
        result.put("cleanResult", cleanResult);

        return R.ok().put("result", result);
    }

}
