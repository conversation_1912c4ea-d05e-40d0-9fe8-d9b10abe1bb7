package com.cjy.mp.modules.place.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.cjy.mp.modules.activity.entity.ActivityGuestEntity;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-09 10:49:40
 */
@Data
@TableName("place_activity_topic")
public class PlaceActivityTopicEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private Long id;
	/**
	 * 会议id
	 */
	private Long activityId;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createOn;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private Long createBy;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.UPDATE)
	private Date updateOn;
	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.UPDATE)
	private Long updateBy;
	/**
	 * 会场名称
	 */
	private String name;
	/**
	 * 
	 */
	private Long placeId;
	/**
	 * 开始时间
	 */
	private Date startTime;
	/**
	 * 结束时间
	 */
	private Date endTime;
	/**
	 * 排序，数值越小越靠前
	 */
	private Integer orderBy;
	/**
	 * 主持别名
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String aliasGuestName;
	/**
	 * 主席别名
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String aliasSpeakerName;
	/**
	 * 讨论别名
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String aliasDiscussName;
	/**
	 * 劳务费
	 */
	@TableField(exist = false)
	private BigDecimal serviceFee;
	/**
	 * 确认状态
	 */
	@TableField(exist = false)
	private Long flagId;
	@TableField(exist = false)
	private Integer confirmStatus;
	/**
	 * 确认时间
	 */
	@TableField(exist = false)
	private Date confirmTime;
	/**
	 * 拒绝理由
	 */
	@TableField(exist = false)
	private String confirmReason;

	/**
	 * 是否重复，查重
	 */
	@TableField(exist = false)
	private Integer isRepeat;

	@TableField(exist = false)
	private String placeName;

	@TableField(exist = false)
	private List<Long> topicGuestIds;

	@TableField(exist = false)
	private List<Long> topicSpeakerIds;

	@TableField(exist = false)
	private List<Long> topicDiscussIds;

	@TableField(exist = false)
	private List<ActivityGuestEntity> activityGuests;

	@TableField(exist = false)
	private List<ActivityGuestEntity> activitySpeakers;

	@TableField(exist = false)
	private List<ActivityGuestEntity> activityDiscuss;
//
//	@TableField(exist = false)
//	private List<ActivityGuestEntity> activityDiscuss;

	@TableField(exist = false)
	private List<PlaceActivityTopicScheduleEntity> placeActivityTopicScheduleEntities;

	@TableField(exist = false)
	private String week;

	/**
	 * 是否重置确认状态 - 前端传递的标志
	 */
	@TableField(exist = false)
	private Boolean resetConfirmStatus;

}
